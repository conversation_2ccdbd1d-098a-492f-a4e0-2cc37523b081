"use client"

import { useState, useEffect, useCallback } from 'react'
import { useQuery } from '@apollo/client'
import { ARTISTS_QUERY } from '@/graphql/queries'
import { Artist } from '../types/artist'

interface UseInfiniteArtistsReturn {
  artists: Artist[]
  loading: boolean
  error: any
  hasNextPage: boolean
  loadMore: () => void
  totalCount: number
  isLoadingMore: boolean
  searchArtists: (searchTerm: string) => void
  isSearching: boolean
  clearSearch: () => void
}

const ITEMS_PER_PAGE = 20

export function useInfiniteArtists(): UseInfiniteArtistsReturn {
  const [allArtists, setAllArtists] = useState<Artist[]>([])
  const [displayedArtists, setDisplayedArtists] = useState<Artist[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [totalCount, setTotalCount] = useState(0)
  const [isSearchMode, setIsSearchMode] = useState(false)
  const [currentSearchTerm, setCurrentSearchTerm] = useState<string>("")

  // Query for artists data (with optional where clause for search)
  const { data, loading, error } = useQuery(ARTISTS_QUERY, {
    variables: {
      where: isSearchMode && currentSearchTerm ? {
        OR: [
          { name: { contains: currentSearchTerm } },
          { artistName: { contains: currentSearchTerm } },
          { bio: { contains: currentSearchTerm } }
        ]
      } : undefined
    },
    onError: (error) => {
      console.error("GraphQL Error:", error)
    }
  })

  // Process Apollo data from connection structure to match Artist interface
  const processApolloArtistData = useCallback((apolloData: typeof data): Artist[] => {
    if (!apolloData?.artistsConnection?.edges) {
      console.warn("No artist data received from Apollo")
      return []
    }

    const processedArtists = apolloData.artistsConnection.edges
      .filter((edge: unknown) => {
        if (typeof edge === 'object' && edge !== null) {
          const e = edge as Record<string, unknown>
          const node = e.node as Record<string, unknown>
          return node && typeof node.mbid === 'string' && (typeof node.name === 'string' || typeof node.artistName === 'string')
        }
        return false
      })
      .map((edge: unknown) => {
        const e = edge as Record<string, unknown>
        const node = e.node as Record<string, unknown>
        return {
          __typename: "Artist",
          id: String(node.mbid), // Use mbid as id
          name: String(node.name || node.artistName),
          bio: node.bio ? String(node.bio) : null,
          formedDate: node.formedDate ? String(node.formedDate) : null,
          disbandedDate: node.disbandedDate ? String(node.disbandedDate) : null,
          location: null, // Not available in Apollo data
          profileImage: node.profileImage ? String(node.profileImage) : null,
          gender: node.gender ? String(node.gender) : null,
          links: node.links ? String(node.links) : null,
        } as Artist
      })

    return processedArtists
  }, [])



  // Update artists when data changes
  useEffect(() => {
    if (data?.artistsConnection) {
      const newArtists = processApolloArtistData(data)

      if (isSearchMode) {
        // For search mode, show all results
        setDisplayedArtists(newArtists)
        setTotalCount(data.artistsConnection.totalCount || 0)
      } else {
        // For normal mode, store all and show first page
        setAllArtists(newArtists)
        setTotalCount(data.artistsConnection.totalCount || 0)

        // Initially show first page
        const firstPageArtists = newArtists.slice(0, ITEMS_PER_PAGE)
        setDisplayedArtists(firstPageArtists)
        setCurrentPage(1)
      }
    }
  }, [data, processApolloArtistData, isSearchMode])

  // Load more function - client-side pagination
  const loadMore = useCallback(() => {
    if (isLoadingMore || loading) return

    const startIndex = currentPage * ITEMS_PER_PAGE
    const endIndex = startIndex + ITEMS_PER_PAGE

    if (startIndex >= allArtists.length) return // No more data

    setIsLoadingMore(true)

    // Simulate loading delay for better UX
    setTimeout(() => {
      const nextPageArtists = allArtists.slice(startIndex, endIndex)
      setDisplayedArtists(prev => [...prev, ...nextPageArtists])
      setCurrentPage(prev => prev + 1)
      setIsLoadingMore(false)

      console.log("🔄 Loaded", nextPageArtists.length, "more artists")
    }, 500)
  }, [isLoadingMore, loading, currentPage, allArtists])

  // Search function
  const searchArtists = useCallback((searchTerm: string) => {
    if (!searchTerm.trim()) {
      clearSearch()
      return
    }

    setCurrentSearchTerm(searchTerm)
    setIsSearchMode(true)
  }, [])

  // Clear search function
  const clearSearch = useCallback(() => {
    setIsSearchMode(false)
    setCurrentSearchTerm("")
    // Reset to showing paginated results from all artists
    const firstPageArtists = allArtists.slice(0, currentPage * ITEMS_PER_PAGE)
    setDisplayedArtists(firstPageArtists)
  }, [allArtists, currentPage])

  // Calculate if there are more pages (only for non-search mode)
  const hasNextPage = !isSearchMode && (currentPage * ITEMS_PER_PAGE) < allArtists.length

  return {
    artists: displayedArtists,
    loading,
    error,
    hasNextPage,
    loadMore,
    totalCount,
    isLoadingMore,
    searchArtists,
    isSearching: loading && isSearchMode,
    clearSearch
  }
}
