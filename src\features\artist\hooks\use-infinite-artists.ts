"use client"

import { useState, useEffect, useCallback } from 'react'
import { useQuery } from '@apollo/client'
import { ARTISTS_QUERY } from '@/graphql/queries'
import { Artist } from '../types/artist'

interface UseInfiniteArtistsReturn {
  artists: Artist[]
  loading: boolean
  error: any
  hasNextPage: boolean
  loadMore: () => void
  totalCount: number
  isLoadingMore: boolean
}

const ITEMS_PER_PAGE = 20

export function useInfiniteArtists(): UseInfiniteArtistsReturn {
  const [allArtists, setAllArtists] = useState<Artist[]>([])
  const [displayedArtists, setDisplayedArtists] = useState<Artist[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [totalCount, setTotalCount] = useState(0)

  // Query for all artists data
  const { data, loading, error } = useQuery(ARTISTS_QUERY, {
    onError: (error) => {
      console.error("GraphQL Error:", error)
    },
    onCompleted: (data) => {
      console.log("GraphQL Data received:", data)
    }
  })

  // Process Apollo data from connection structure to match Artist interface
  const processApolloArtistData = useCallback((apolloData: typeof data): Artist[] => {
    if (!apolloData?.artistsConnection?.edges) {
      console.warn("No artist data received from Apollo")
      return []
    }

    const processedArtists = apolloData.artistsConnection.edges
      .filter((edge: unknown) => {
        if (typeof edge === 'object' && edge !== null) {
          const e = edge as Record<string, unknown>
          const node = e.node as Record<string, unknown>
          return node && typeof node.mbid === 'string' && (typeof node.name === 'string' || typeof node.artistName === 'string')
        }
        return false
      })
      .map((edge: unknown) => {
        const e = edge as Record<string, unknown>
        const node = e.node as Record<string, unknown>
        return {
          __typename: "Artist",
          id: String(node.mbid), // Use mbid as id
          name: String(node.name || node.artistName),
          bio: node.bio ? String(node.bio) : null,
          formedDate: node.formedDate ? String(node.formedDate) : null,
          disbandedDate: node.disbandedDate ? String(node.disbandedDate) : null,
          location: null, // Not available in Apollo data
          profileImage: node.profileImage ? String(node.profileImage) : null,
          gender: node.gender ? String(node.gender) : null,
          links: node.links ? String(node.links) : null,
        } as Artist
      })

    console.log("✅ Successfully processed", processedArtists.length, "artists from Apollo connection")
    return processedArtists
  }, [])

  // Update artists when data changes
  useEffect(() => {
    if (data?.artistsConnection) {
      console.log("🔍 Raw data received:", data.artistsConnection)
      const newArtists = processApolloArtistData(data)
      console.log("🔍 Processed artists:", newArtists)
      setAllArtists(newArtists)
      setTotalCount(data.artistsConnection.totalCount || 0)

      // Initially show first page
      const firstPageArtists = newArtists.slice(0, ITEMS_PER_PAGE)
      setDisplayedArtists(firstPageArtists)
      setCurrentPage(1)

      console.log("📊 Total artists available:", data.artistsConnection.totalCount)
      console.log("✅ Initially showing", firstPageArtists.length, "artists")
    } else {
      console.log("❌ No data received or artistsConnection is null")
    }
  }, [data, processApolloArtistData])

  // Load more function - client-side pagination
  const loadMore = useCallback(() => {
    if (isLoadingMore || loading) return

    const startIndex = currentPage * ITEMS_PER_PAGE
    const endIndex = startIndex + ITEMS_PER_PAGE

    if (startIndex >= allArtists.length) return // No more data

    setIsLoadingMore(true)

    // Simulate loading delay for better UX
    setTimeout(() => {
      const nextPageArtists = allArtists.slice(startIndex, endIndex)
      setDisplayedArtists(prev => [...prev, ...nextPageArtists])
      setCurrentPage(prev => prev + 1)
      setIsLoadingMore(false)

      console.log("🔄 Loaded", nextPageArtists.length, "more artists")
    }, 500)
  }, [isLoadingMore, loading, currentPage, allArtists])

  // Calculate if there are more pages
  const hasNextPage = (currentPage * ITEMS_PER_PAGE) < allArtists.length

  return {
    artists: displayedArtists,
    loading,
    error,
    hasNextPage,
    loadMore,
    totalCount,
    isLoadingMore
  }
}
