"use client"

import { useState, useEffect, useCallback } from 'react'
import { useQuery } from '@apollo/client'
import { ARTISTS_QUERY } from '@/graphql/queries'
import { Artist } from '../types/artist'

interface UseInfiniteArtistsReturn {
  artists: Artist[]
  loading: boolean
  error: any
  hasNextPage: boolean
  loadMore: () => void
  totalCount: number
  isLoadingMore: boolean
  searchArtists: (searchTerm: string) => void
  isSearching: boolean
  clearSearch: () => void
}

const ITEMS_PER_PAGE = 20

export function useInfiniteArtists(): UseInfiniteArtistsReturn {
  const [displayedArtists, setDisplayedArtists] = useState<Artist[]>([])
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [totalCount, setTotalCount] = useState(0)
  const [isSearchMode, setIsSearchMode] = useState(false)
  const [currentSearchTerm, setCurrentSearchTerm] = useState<string>("")
  const [endCursor, setEndCursor] = useState<string | null>(null)
  const [hasNextPage, setHasNextPage] = useState(true)

  // Query for artists data (with pagination and optional where clause for search)
  const { data, loading, error, fetchMore } = useQuery(ARTISTS_QUERY, {
    variables: {
      first: ITEMS_PER_PAGE,
      after: null,
      where: isSearchMode && currentSearchTerm ? {
        OR: [
          { artistName_CONTAINS: currentSearchTerm },
          { artistName_CONTAINS: currentSearchTerm.toLowerCase() },
          { artistName_CONTAINS: currentSearchTerm.toUpperCase() },
          { artistName_CONTAINS: currentSearchTerm.charAt(0).toUpperCase() + currentSearchTerm.slice(1).toLowerCase() },
          { name_CONTAINS: currentSearchTerm },
          { name_CONTAINS: currentSearchTerm.toLowerCase() },
          { name_CONTAINS: currentSearchTerm.toUpperCase() },
          { name_CONTAINS: currentSearchTerm.charAt(0).toUpperCase() + currentSearchTerm.slice(1).toLowerCase() }
        ]
      } : undefined
    },
    notifyOnNetworkStatusChange: true,
    onError: (error) => {
      console.error("GraphQL Error:", error)
      console.error("Search term:", currentSearchTerm)
    },
    onCompleted: (data) => {
      if (isSearchMode) {
        console.log("Search completed for:", currentSearchTerm, "- Found:", data?.artistsConnection?.totalCount || 0, "results")
      }
    }
  })

  // Process Apollo data from connection structure to match Artist interface
  const processApolloArtistData = useCallback((apolloData: typeof data): Artist[] => {
    if (!apolloData?.artistsConnection?.edges) {
      console.warn("No artist data received from Apollo")
      return []
    }

    const processedArtists = apolloData.artistsConnection.edges
      .filter((edge: unknown) => {
        if (typeof edge === 'object' && edge !== null) {
          const e = edge as Record<string, unknown>
          const node = e.node as Record<string, unknown>
          return node && typeof node.mbid === 'string' && (typeof node.name === 'string' || typeof node.artistName === 'string')
        }
        return false
      })
      .map((edge: unknown) => {
        const e = edge as Record<string, unknown>
        const node = e.node as Record<string, unknown>
        return {
          __typename: "Artist",
          id: String(node.mbid), // Use mbid as id
          name: String(node.name || node.artistName),
          bio: node.bio ? String(node.bio) : null,
          formedDate: node.formedDate ? String(node.formedDate) : null,
          disbandedDate: node.disbandedDate ? String(node.disbandedDate) : null,
          location: null, // Not available in Apollo data
          profileImage: node.profileImage ? String(node.profileImage) : null,
          gender: node.gender ? String(node.gender) : null,
          links: node.links ? String(node.links) : null,
        } as Artist
      })

    return processedArtists
  }, [])



  // Update artists when data changes
  useEffect(() => {
    if (data?.artistsConnection) {
      const newArtists = processApolloArtistData(data)

      // For initial load or search, replace all displayed artists
      setDisplayedArtists(newArtists)
      setTotalCount(data.artistsConnection.totalCount || 0)
      setHasNextPage(data.artistsConnection.pageInfo?.hasNextPage || false)
      setEndCursor(data.artistsConnection.pageInfo?.endCursor || null)
    }
  }, [data, processApolloArtistData])

  // Load more function - GraphQL pagination
  const loadMore = useCallback(async () => {
    if (!hasNextPage || isLoadingMore || loading) return

    setIsLoadingMore(true)
    try {
      const result = await fetchMore({
        variables: {
          first: ITEMS_PER_PAGE,
          after: endCursor,
          where: isSearchMode && currentSearchTerm ? {
            OR: [
              { artistName_CONTAINS: currentSearchTerm },
              { artistName_CONTAINS: currentSearchTerm.toLowerCase() },
              { artistName_CONTAINS: currentSearchTerm.toUpperCase() },
              { artistName_CONTAINS: currentSearchTerm.charAt(0).toUpperCase() + currentSearchTerm.slice(1).toLowerCase() },
              { name_CONTAINS: currentSearchTerm },
              { name_CONTAINS: currentSearchTerm.toLowerCase() },
              { name_CONTAINS: currentSearchTerm.toUpperCase() },
              { name_CONTAINS: currentSearchTerm.charAt(0).toUpperCase() + currentSearchTerm.slice(1).toLowerCase() }
            ]
          } : undefined
        }
      })

      if (result.data?.artistsConnection) {
        const newArtists = processApolloArtistData(result.data)
        console.log("📦 FetchMore result:", {
          newArtistsCount: newArtists.length,
          hasNextPage: result.data.artistsConnection.pageInfo?.hasNextPage,
          endCursor: result.data.artistsConnection.pageInfo?.endCursor
        })
        setDisplayedArtists(prev => {
          const updated = [...prev, ...newArtists]
          console.log("📊 Updated displayed artists:", prev.length, "→", updated.length)
          return updated
        })
        setHasNextPage(result.data.artistsConnection.pageInfo?.hasNextPage || false)
        setEndCursor(result.data.artistsConnection.pageInfo?.endCursor || null)
        console.log("Loaded", newArtists.length, "more artists")
      }
    } catch (err) {
      console.error("Error loading more artists:", err)
    } finally {
      setIsLoadingMore(false)
    }
  }, [hasNextPage, isLoadingMore, loading, endCursor, fetchMore, processApolloArtistData, isSearchMode, currentSearchTerm])

  // Search function
  const searchArtists = useCallback((searchTerm: string) => {
    if (!searchTerm.trim()) {
      setIsSearchMode(false)
      setCurrentSearchTerm("")
      setEndCursor(null)
      return
    }

    setCurrentSearchTerm(searchTerm)
    setIsSearchMode(true)
    setEndCursor(null) // Reset pagination for new search
  }, [])

  // Clear search function
  const clearSearch = useCallback(() => {
    setIsSearchMode(false)
    setCurrentSearchTerm("")
    setEndCursor(null)
    // This will trigger a refetch with no where clause
  }, [])

  return {
    artists: displayedArtists,
    loading,
    error,
    hasNextPage,
    loadMore,
    totalCount,
    isLoadingMore,
    searchArtists,
    isSearching: loading && isSearchMode,
    clearSearch
  }
}
