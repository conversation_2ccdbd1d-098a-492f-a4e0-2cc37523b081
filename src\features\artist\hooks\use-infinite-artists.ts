"use client"

import { useState, useEffect, useCallback, useRef } from 'react'
import { useQuery } from '@apollo/client'
import { ARTISTS_PAGINATED_QUERY } from '@/graphql/queries'
import { Artist } from '../types/artist'

interface UseInfiniteArtistsReturn {
  artists: Artist[]
  loading: boolean
  error: any
  hasNextPage: boolean
  loadMore: () => void
  totalCount: number
  isLoadingMore: boolean
}

const ITEMS_PER_PAGE = 20

export function useInfiniteArtists(): UseInfiniteArtistsReturn {
  const [artists, setArtists] = useState<Artist[]>([])
  const [hasNextPage, setHasNextPage] = useState(true)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [endCursor, setEndCursor] = useState<string | null>(null)
  const [totalCount, setTotalCount] = useState(0)

  // Query for initial load and subsequent pages
  const { data, loading, error, fetchMore } = useQuery(ARTISTS_PAGINATED_QUERY, {
    variables: {
      first: ITEMS_PER_PAGE,
      after: null
    },
    notifyOnNetworkStatusChange: true,
  })

  // Process Apollo data from connection structure to match Artist interface
  const processApolloArtistData = useCallback((apolloData: typeof data): Artist[] => {
    if (!apolloData?.artistsConnection?.edges) {
      console.warn("No artist data received from Apollo")
      return []
    }

    const processedArtists = apolloData.artistsConnection.edges
      .filter((edge: unknown) => {
        if (typeof edge === 'object' && edge !== null) {
          const e = edge as Record<string, unknown>
          const node = e.node as Record<string, unknown>
          return node && typeof node.mbid === 'string' && (typeof node.name === 'string' || typeof node.artistName === 'string')
        }
        return false
      })
      .map((edge: unknown) => {
        const e = edge as Record<string, unknown>
        const node = e.node as Record<string, unknown>
        return {
          __typename: "Artist",
          id: String(node.mbid), // Use mbid as id
          name: String(node.name || node.artistName),
          bio: node.bio ? String(node.bio) : null,
          formedDate: node.formedDate ? String(node.formedDate) : null,
          disbandedDate: node.disbandedDate ? String(node.disbandedDate) : null,
          location: null, // Not available in Apollo data
          profileImage: node.profileImage ? String(node.profileImage) : null,
          gender: node.gender ? String(node.gender) : null,
          links: node.links ? String(node.links) : null,
        } as Artist
      })

    console.log("✅ Successfully processed", processedArtists.length, "artists from Apollo connection")
    return processedArtists
  }, [])

  // Update artists when data changes
  useEffect(() => {
    if (data?.artistsConnection) {
      const newArtists = processApolloArtistData(data)
      setArtists(newArtists)
      setHasNextPage(data.artistsConnection.pageInfo?.hasNextPage || false)
      setEndCursor(data.artistsConnection.pageInfo?.endCursor || null)
      setTotalCount(data.artistsConnection.totalCount || 0)
      console.log("📊 Total artists available:", data.artistsConnection.totalCount)
    }
  }, [data, processApolloArtistData])

  // Load more function
  const loadMore = useCallback(async () => {
    if (!hasNextPage || isLoadingMore || loading) return

    setIsLoadingMore(true)
    try {
      const result = await fetchMore({
        variables: {
          first: ITEMS_PER_PAGE,
          after: endCursor
        }
      })

      if (result.data?.artistsConnection) {
        const newArtists = processApolloArtistData(result.data)
        setArtists(prev => [...prev, ...newArtists])
        setHasNextPage(result.data.artistsConnection.pageInfo?.hasNextPage || false)
        setEndCursor(result.data.artistsConnection.pageInfo?.endCursor || null)
        console.log("🔄 Loaded", newArtists.length, "more artists")
      }
    } catch (err) {
      console.error("Error loading more artists:", err)
    } finally {
      setIsLoadingMore(false)
    }
  }, [hasNextPage, isLoadingMore, loading, endCursor, fetchMore, processApolloArtistData])

  return {
    artists,
    loading,
    error,
    hasNextPage,
    loadMore,
    totalCount,
    isLoadingMore
  }
}
