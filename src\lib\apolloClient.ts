import { ApolloClient, InMemoryCache, HttpLink } from '@apollo/client';

const httpLink = new HttpLink({
  uri: 'https://iptd5paa92.execute-api.us-east-2.amazonaws.com/beta/graphql',
});

// Optional: Add auth or API key headers
// const authLink = new ApolloLink((operation, forward) => {
//   operation.setContext(({ headers = {} }) => ({
//     headers: {
//       ...headers,
//       // Authorization: `Bearer ${process.env.NEXT_PUBLIC_AUTH_TOKEN}`,
//       // 'x-api-key': process.env.NEXT_PUBLIC_API_KEY,
//     },
//   }));
//   return forward(operation);
// });

// const link = authLink.concat(httpLink);
const link = httpLink;

export const apolloClient = new ApolloClient({
  link,
  cache: new InMemoryCache(),
});
