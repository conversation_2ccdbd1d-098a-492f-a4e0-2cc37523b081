"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Search, Users, Filter } from "lucide-react"
import { ArtistCard } from "./artist-card"
import { ARTISTS_QUERY } from "@/graphql/queries"
import { Artist } from "../types/artist"
import { Skeleton } from "@/components/ui/skeleton"
import { useRouter } from "next/navigation"
import { useQuery } from '@apollo/client';


export default function ArtistsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const router = useRouter();

  // Apollo Client fetch for artists
  const { data: apolloArtistsData, loading, error } = useQuery(ARTISTS_QUERY);

  // Process Apollo data from connection structure to match Artist interface
  const processApolloArtistData = (apolloData: typeof apolloArtistsData): Artist[] => {
    if (!apolloData?.artistsConnection?.edges) {
      console.warn("No artist data received from Apollo");
      return [];
    }

    const artists = apolloData.artistsConnection.edges
      .filter((edge: unknown) => {
        if (typeof edge === 'object' && edge !== null) {
          const e = edge as Record<string, unknown>;
          const node = e.node as Record<string, unknown>;
          return node && typeof node.mbid === 'string' && (typeof node.name === 'string' || typeof node.artistName === 'string');
        }
        return false;
      })
      .map((edge: unknown) => {
        const e = edge as Record<string, unknown>;
        const node = e.node as Record<string, unknown>;
        return {
          __typename: "Artist",
          id: String(node.mbid), // Use mbid as id
          name: String(node.name || node.artistName),
          bio: node.bio ? String(node.bio) : null,
          formedDate: node.formedDate ? String(node.formedDate) : null,
          disbandedDate: node.disbandedDate ? String(node.disbandedDate) : null,
          location: null, // Not available in Apollo data
          profileImage: node.profileImage ? String(node.profileImage) : null,
          gender: node.gender ? String(node.gender) : null,
          links: node.links ? String(node.links) : null,
        } as Artist;
      });

    console.log("✅ Successfully processed", artists.length, "artists from Apollo connection");
    console.log("📊 Total artists available:", apolloData.artistsConnection.totalCount);
    return artists;
  };

  // Get processed artist data
  const artistApiData = processApolloArtistData(apolloArtistsData);

  // Filter artists based on search
  const filteredArtists = artistApiData.filter((artist) => {
    return artist.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (artist.bio?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false)
  })

  return (
    <div className="flex flex-col h-full w-full overflow-hidden">
      <div className="flex flex-col h-full p-2">

        {/* Search and Filters */}
        <Card className="mb-4 flex-shrink-0">
          <CardContent className="pt-4 pb-4">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search artists by name or bio..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>

            {/* Search Summary */}
            <div className="flex items-center gap-4 mt-3 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <Filter className="w-4 h-4" />
                <span>
                  Showing {filteredArtists.length} of {apolloArtistsData?.artistsConnection?.totalCount || artistApiData.length} artists
                </span>
              </div>
              {searchTerm && <span>• Searching for {searchTerm}</span>}
            </div>
          </CardContent>
        </Card>

        {/* Artists Grid */}
        <div className="flex-1 min-h-0 overflow-auto">
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 h-full overflow-y-auto pr-2">
              {Array.from({ length: 8 }).map((_, i) => (
                <Card key={i} className="h-fit">
                  <CardContent className="pt-4 pb-4">
                    <Skeleton className="h-24 w-full mb-3" />
                    <Skeleton className="h-5 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-1/2" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full">
              <Card className="text-center py-8">
                <CardContent>
                  <Users className="w-10 h-10 text-destructive mx-auto mb-3" />
                  <h3 className="text-lg font-semibold mb-2">Error loading artists</h3>
                  <p className="text-muted-foreground text-sm">
                    {error.message || "Failed to fetch artists. Please try again later."}
                  </p>
                </CardContent>
              </Card>
            </div>
          ) : filteredArtists.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 h-full overflow-y-auto pr-2">
              {filteredArtists.map((artist) => (
                <div key={artist.id} onClick={() => router.push(`/artist/?id=${artist.id}`)} className="cursor-pointer h-fit">
                  <ArtistCard artist={artist} />
                </div>
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <Card className="text-center py-8">
                <CardContent>
                  <Users className="w-10 h-10 text-muted-foreground mx-auto mb-3" />
                  <h3 className="text-lg font-semibold mb-2">No artists found</h3>
                  <p className="text-muted-foreground text-sm">
                    {searchTerm
                      ? "Try adjusting your search criteria."
                      : "No artists available at the moment."}
                  </p>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
