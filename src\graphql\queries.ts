/* tslint:disable */
// this is an auto generated file. This will be overwritten

import * as APITypes from "../API";
import { gql } from '@apollo/client';

type GeneratedQuery<InputType, OutputType> = string & {
  __generatedQueryInput: InputType;
  __generatedQueryOutput: OutputType;
};

export const getAllArtist = /* GraphQL */ `query GetAllArtist {
  getAllArtist {
    id
    name
    bio
    formedDate
    disbandedDate
    location
    __typename
  }
}
` as GeneratedQuery<
  APITypes.GetAllArtistQueryVariables,
  APITypes.GetAllArtistQuery
>;
export const searchArtistByName = /* GraphQL */ `query SearchArtistByName($name: String!) {
  searchArtistByName(name: $name) {
    id
    name
    bio
    formedDate
    disbandedDate
    location
    __typename
  }
}
` as GeneratedQuery<
  APITypes.SearchArtistByNameQueryVariables,
  APITypes.SearchArtistByNameQuery
>;
export const getArtistProfile = /* GraphQL */ `query GetArtistProfile($artistId: ID!) {
  getArtistProfile(artistId: $artistId) {
    artist {
      id
      name
      bio
      formedDate
      disbandedDate
      location
      dp
      __typename
    }
    songs {
      id
      title
      duration
      recordID
      releaseDate
      coverPhoto
      role
      credits{
        artistId
        role
        name
      }
      __typename
    }
    albums {
      id
      title
      releaseDate
      genre
      description
      coverArtData
      __typename
    }
    __typename
  }
}
` as GeneratedQuery<
  APITypes.GetArtistProfileQueryVariables,
  APITypes.GetArtistProfileQuery
>;
export const getAlbum = /* GraphQL */ `query GetAlbum($albumId: ID!) {
  getAlbum(albumId: $albumId) {
    id
    title
    releaseDate
    genre
    description
    coverArtData
    tracks {
      trackPosition
        recording{
        id
        title	
        duration
        releaseDate
      }
      __typename
    }
    __typename
  }
}
` as GeneratedQuery<APITypes.GetAlbumQueryVariables, APITypes.GetAlbumQuery>;
export const getSong = /* GraphQL */ `query GetSong($songId: ID!) {
  getSong(songId: $songId) {
    id
    title
    duration
    recordID
    releaseDate
    coverPhoto
    role
    credits {
      artistId
      role
      name
      __typename
    }
    recordings {
      id
      title
      duration
      recordID
      releaseDate
      coverPhoto
      __typename
    }
    __typename
  }
}
` as GeneratedQuery<APITypes.GetSongQueryVariables, APITypes.GetSongQuery>;
export const getRecording = /* GraphQL */ `query GetRecording($recordingId: ID!) {
  getRecording(recordingId: $recordingId) {
    recording {
      id
      title
      duration
      recordID
      releaseDate
      coverPhoto
          credits{
      artistId
      role
      name
      }
      __typename
    }
    performedBy {
      id
      name
      bio
      formedDate
      disbandedDate
      location
      __typename
    }
    song {
      id
      title
      duration
      recordID
      releaseDate
      coverPhoto
      role
      credits{
      artistId
      role
      name
      }
      __typename
    }
    similarRecordings {
      id
      title
      duration
      recordID
      releaseDate
      coverPhoto
      __typename
    }
    __typename
  }
}
` as GeneratedQuery<
  APITypes.GetRecordingQueryVariables,
  APITypes.GetRecordingQuery
>;

export const ARTISTS_QUERY = gql`
  query ArtistsConnection($where: ArtistWhereInput) {
    artistsConnection(where: $where) {
      totalCount
      edges {
        cursor
        node {
          mbid
          name
          artistName
          gender
          bio
          formedDate
          disbandedDate
          profileImage
          links
        }
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
    }
  }
`;



export const ARTIST_PROFILE_QUERY = gql`
  query Works($artistId: ID!) {
    artistsConnection(where: { mbid: $artistId }) {
      edges {
        node {
          mbid
          name
          artistName
          bio
          formedDate
          disbandedDate
          profileImage
          creditedOnSongConnection {
            edges {
              role
              node {
                mbid
                title
                coverImage
                recordings {
                  mbid
                  title
                  duration
                  releaseDate
                  coverImage
                }
              }
            }
          }
          albums {
            mbid
            title
            releaseDate
            coverArtData
          }
        }
      }
    }
  }
`;

export const ALBUM_QUERY = gql`
  query AlbumsConnection($albumId: ID!) {
    albumsConnection(where: { mbid: $albumId }) {
      totalCount
      edges {
        node {
          mbid
          title
          releaseDate
          coverArtData
          tracks {
            mbid
            title
            duration
            recordID
            releaseDate
            coverImage
          }
        }
      }
    }
  }
`;

export const SONG_QUERY = gql`
  query SongsConnection($songId: ID!) {
    songsConnection(where: { mbid: $songId }) {
      edges {
        cursor
        node {
          mbid
          title
          coverImage
          recordings {
            mbid
            title
            duration
            recordID
            releaseDate
            coverImage
          }
          creditedOnSong {
            mbid
            name
            artistName
            gender
            bio
            formedDate
            disbandedDate
            profileImage
            links
          }
        }
      }
    }
  }
`;

export const RECORDING_QUERY = gql`
 query RecordingsConnection($recordingId: ID!) {
    recordingsConnection(where: { mbid: $recordingId }) {
        edges {
            cursor
            node {
                mbid
                title
                duration
                recordID
                releaseDate
                coverImage
                song {
                    mbid
                    title
                    coverImage
                    recordings {
                        mbid
                        title
                        duration
                        recordID
                        releaseDate
                        coverImage
                        songConnection {
                            edges {
                                node {
                                    creditedOnSongConnection {
                                        edges {
                                            node {
                                                mbid
                                                name
                                                artistName
                                                profileImage
                                                links
                                            }
                                            role
                                            as
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                creditedOnRecordingConnection {
                    edges {
                        role
                        as
                        node {
                            mbid
                            name
                            artistName
                            profileImage
                            links
                        }
                    }
                }
            }
        }
    }
}

`;
